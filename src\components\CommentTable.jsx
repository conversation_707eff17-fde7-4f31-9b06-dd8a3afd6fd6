import React, { useState } from 'react';
import { Check, X, User, Calendar, Search, Filter } from 'lucide-react';

const CommentTable = ({ comments = [], status, onAction }) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [sortBy, setSortBy] = useState('createdAt');
  const [sortOrder, setSortOrder] = useState('desc');

  const getSentimentEmoji = (sentiment) => {
    if (!sentiment) return '😐';

    // Handle both string and object formats
    if (typeof sentiment === 'string') {
      if (sentiment.includes('positive')) return '😊';
      if (sentiment.includes('negative')) return '😞';
      return '😐';
    }

    const score = sentiment.score || 0;
    if (score > 0.1) return '😊';
    if (score < -0.1) return '😞';
    return '😐';
  };

  const getToxicityColor = (toxicity) => {
    if (!toxicity) return 'text-gray-400';

    // Handle both string and object formats
    if (typeof toxicity === 'string') {
      if (toxicity.includes('toxic')) return 'text-red-400';
      return 'text-green-400';
    }

    const score = toxicity.score || 0;
    if (score > 0.7) return 'text-red-400';
    if (score > 0.4) return 'text-yellow-400';
    return 'text-green-400';
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const filteredComments = comments
    .filter(comment =>
      comment.content.toLowerCase().includes(searchTerm.toLowerCase()) ||
      comment.author?.name?.toLowerCase().includes(searchTerm.toLowerCase())
    )
    .sort((a, b) => {
      let aValue = a[sortBy];
      let bValue = b[sortBy];
      
      if (sortBy === 'author') {
        aValue = a.author?.name || '';
        bValue = b.author?.name || '';
      } else if (sortBy === 'sentiment') {
        aValue = a.sentiment?.score || 0;
        bValue = b.sentiment?.score || 0;
      } else if (sortBy === 'toxicity') {
        aValue = a.toxicity?.score || 0;
        bValue = b.toxicity?.score || 0;
      }
      
      if (sortOrder === 'asc') {
        return aValue > bValue ? 1 : -1;
      } else {
        return aValue < bValue ? 1 : -1;
      }
    });

  if (comments.length === 0) {
    return (
      <div className="text-center py-12">
        <div className="text-6xl mb-4">
          {status === 'flagged' ? '🚩' : status === 'removed' ? '🗑️' : '✅'}
        </div>
        <h3 className="text-xl font-semibold text-gray-400 mb-2">
          No {status} comments
        </h3>
        <p className="text-gray-500">
          {status === 'flagged' 
            ? 'All comments are currently clean!'
            : status === 'removed'
            ? 'No comments have been removed yet.'
            : 'No allowed comments to display.'
          }
        </p>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {/* Search and Filter */}
      <div className="flex flex-col sm:flex-row gap-4 mb-6">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
          <input
            type="text"
            placeholder="Search comments or authors..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full pl-10 pr-4 py-2 bg-gray-800 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        </div>
        
        <div className="flex gap-2">
          <select
            value={sortBy}
            onChange={(e) => setSortBy(e.target.value)}
            className="px-3 py-2 bg-gray-800 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="createdAt">Date</option>
            <option value="author">Author</option>
            <option value="sentiment">Sentiment</option>
            <option value="toxicity">Toxicity</option>
          </select>
          
          <button
            onClick={() => setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc')}
            className="px-3 py-2 bg-gray-800 border border-gray-600 rounded-lg text-white hover:bg-gray-700 transition-colors"
          >
            {sortOrder === 'asc' ? '↑' : '↓'}
          </button>
        </div>
      </div>

      {/* Table */}
      <div className="overflow-x-auto">
        <table className="w-full">
          <thead>
            <tr className="border-b border-gray-700">
              <th className="text-left py-3 px-4 text-gray-300 font-medium">Author</th>
              <th className="text-left py-3 px-4 text-gray-300 font-medium">Comment</th>
              <th className="text-center py-3 px-4 text-gray-300 font-medium">Sentiment</th>
              <th className="text-center py-3 px-4 text-gray-300 font-medium">Toxicity</th>
              <th className="text-center py-3 px-4 text-gray-300 font-medium">Date</th>
              {status === 'flagged' && (
                <th className="text-center py-3 px-4 text-gray-300 font-medium">Actions</th>
              )}
            </tr>
          </thead>
          <tbody>
            {filteredComments.map((comment) => (
              <tr key={comment.id} className="border-b border-gray-800 hover:bg-gray-800/50 transition-colors">
                <td className="py-4 px-4">
                  <div className="flex items-center space-x-3">
                    <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center">
                      <User className="w-4 h-4 text-white" />
                    </div>
                    <div>
                      <div className="text-white font-medium">
                        {comment.author?.name || 'Anonymous'}
                      </div>
                      <div className="text-xs text-gray-400">
                        {comment.author?.email || 'No email'}
                      </div>
                    </div>
                  </div>
                </td>
                
                <td className="py-4 px-4 max-w-md">
                  <p className="text-gray-200 line-clamp-3">
                    {comment.content}
                  </p>
                </td>
                
                <td className="py-4 px-4 text-center">
                  <div className="flex flex-col items-center space-y-1">
                    <span className="text-lg">{getSentimentEmoji(comment.sentiment)}</span>
                    <span className="text-xs text-gray-400">
                      {typeof comment.sentiment === 'string'
                        ? comment.sentiment.slice(0, 8)
                        : comment.sentiment?.score?.toFixed(2) || '0.00'}
                    </span>
                  </div>
                </td>

                <td className="py-4 px-4 text-center">
                  <div className="flex flex-col items-center space-y-1">
                    <div className={`w-3 h-3 rounded-full ${
                      typeof comment.toxicity === 'string'
                        ? (comment.toxicity.includes('toxic') ? 'bg-red-400' : 'bg-green-400')
                        : (comment.toxicity?.score > 0.7 ? 'bg-red-400' :
                           comment.toxicity?.score > 0.4 ? 'bg-yellow-400' : 'bg-green-400')
                    }`}></div>
                    <span className={`text-xs ${getToxicityColor(comment.toxicity)}`}>
                      {typeof comment.toxicity === 'string'
                        ? comment.toxicity.slice(0, 8)
                        : comment.toxicity?.score?.toFixed(2) || '0.00'}
                    </span>
                  </div>
                </td>
                
                <td className="py-4 px-4 text-center">
                  <div className="flex flex-col items-center space-y-1">
                    <Calendar className="w-4 h-4 text-gray-400" />
                    <span className="text-xs text-gray-400">
                      {formatDate(comment.createdAt)}
                    </span>
                  </div>
                </td>
                
                {status === 'flagged' && (
                  <td className="py-4 px-4">
                    <div className="flex justify-center space-x-2">
                      <button
                        onClick={() => onAction(comment.id, 'allow')}
                        className="flex items-center space-x-1 bg-green-600 hover:bg-green-700 text-white px-3 py-1 rounded-lg text-sm transition-colors"
                        title="Allow comment"
                      >
                        <Check className="w-3 h-3" />
                        <span>Allow</span>
                      </button>
                      
                      <button
                        onClick={() => onAction(comment.id, 'remove')}
                        className="flex items-center space-x-1 bg-red-600 hover:bg-red-700 text-white px-3 py-1 rounded-lg text-sm transition-colors"
                        title="Remove comment"
                      >
                        <X className="w-3 h-3" />
                        <span>Remove</span>
                      </button>
                    </div>
                  </td>
                )}
              </tr>
            ))}
          </tbody>
        </table>
      </div>
      
      {filteredComments.length === 0 && searchTerm && (
        <div className="text-center py-8">
          <p className="text-gray-400">No comments found matching "{searchTerm}"</p>
        </div>
      )}
    </div>
  );
};

export default CommentTable;
